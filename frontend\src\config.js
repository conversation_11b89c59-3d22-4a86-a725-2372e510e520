/**
 * Application Configuration
 *
 * This file centralizes all environment variables and configuration settings.
 * It provides a single source of truth for application configuration.
 */

// Configuration object
const config = {
  ENV: import.meta.env.VITE_ENV,
  API_URL: import.meta.env.VITE_API_URL,
  LOGIN_URL: import.meta.env.VITE_LOGIN_URL,
  FORGET_URL: import.meta.env.VITE_FORGET_URL,
  READINGEXAM_URL: import.meta.env.VITE_READINGEXAM_URL,
  PAYEMENT_URL: import.meta.env.VITE_PAYEMENT_URL,
  MONITOR_URL: import.meta.env.VITE_MONITOR_URL,
  READING_EVEALUTION_URL: import.meta.env.VITE_READING_EVEALUTION_URL,

  // Authentication settings (for backward compatibility)
  AUTH: {
    TOKEN_KEY: 'token',
    USER_KEY: 'user',
    MAIN_CODE_KEY: 'main_code',
  },
};

export default config;
